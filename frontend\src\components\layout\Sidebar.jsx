/**
 * Sidebar Component
 *
 * A reusable sidebar navigation component with role-based menu items.
 * Supports role-specific styling and navigation for different user panels.
 */

import { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Button from '../field/Button';
import config from '@/config';

const Sidebar = ({
  isOpen = true,
  onToggle,
  className = '',
  variant = 'default' // default, compact, floating
}) => {
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState({});

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleSubmenu = (menuKey) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }));
  };

  const isActiveRoute = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  // Role-specific navigation items
  const getNavigationItems = () => {
    const baseItems = [
      {
        key: 'dashboard',
        label: 'Dashboard',
        path: '/dashboard',
        icon: (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          </svg>
        )
      }
    ];

    if (!currentUser) return baseItems;

    switch (currentUser.role) {
      case 'Super Admin':
        return [
          ...baseItems,
          {
            key: 'users',
            label: 'User Management',
            path: '/dashboard?tab=users',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            )
          },
          {
            key: 'courses',
            label: 'Course Management',
            path: '/dashboard?tab=courses',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            )
          },
          {
            key: 'students',
            label: 'Student Management',
            path: '/dashboard?tab=students',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )
          },
          {
            key: 'parents',
            label: 'Parent Management',
            path: '/dashboard?tab=parents',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            )
          }
        ];

      case 'Admin':
        return [
          ...baseItems,
          {
            key: 'teachers',
            label: 'Teacher Management',
            path: '/dashboard?tab=teachers',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )
          },
          {
            key: 'students',
            label: 'Student Management',
            path: '/dashboard?tab=students',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )
          },
          {
            key: 'courses',
            label: 'Course Management',
            path: '/dashboard?tab=courses',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            )
          },
          {
            key: 'parents',
            label: 'Parent Management',
            path: '/dashboard?tab=parents',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            )
          }
        ];

      case 'Teacher':
        return [
          ...baseItems,
          {
            key: 'students',
            label: 'Student Management',
            path: '/dashboard?tab=students',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )
          },
          {
            key: 'courses',
            label: 'Course Management',
            path: '/dashboard?tab=courses',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            )
          },
          {
            key: 'parents',
            label: 'Parent Management',
            path: '/dashboard?tab=parents',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            )
          }
        ];

      case 'Student':
        return [
          ...baseItems,
          {
            key: 'courses',
            label: 'My Courses',
            path: '/dashboard?tab=courses',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            )
          },
          {
            key: 'profile',
            label: 'My Profile',
            path: '/dashboard?tab=profile',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )
          }
        ];

      case 'Parent':
        return [
          ...baseItems,
          {
            key: 'children',
            label: 'My Children',
            path: '/dashboard?tab=children',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )
          },
          {
            key: 'profile',
            label: 'My Profile',
            path: '/dashboard?tab=profile',
            icon: (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )
          }
        ];

      default:
        return baseItems;
    }
  };

  // Get role-specific styling
  const getRoleTheme = () => {
    if (!currentUser) return 'bg-gray-800 text-white';

    switch (currentUser.role) {
      case 'Super Admin':
        return 'bg-purple-800 text-white';
      case 'Admin':
        return 'bg-green-800 text-white';
      case 'Teacher':
        return 'bg-indigo-800 text-white';
      case 'Student':
        return 'bg-blue-800 text-white';
      case 'Parent':
        return 'bg-orange-800 text-white';
      default:
        return 'bg-gray-800 text-white';
    }
  };

  const navigationItems = getNavigationItems();
  const themeClasses = getRoleTheme();

  // Variant-specific classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'compact':
        return 'w-16';
      case 'floating':
        return 'w-64 rounded-lg shadow-xl m-4';
      default:
        return 'w-64';
    }
  };

  const variantClasses = getVariantClasses();

  return (
    <aside
      className={`${themeClasses} ${variantClasses} flex-shrink-0 transition-all duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } md:translate-x-0 fixed md:static h-full z-20 ${className}`}
    >
      {/* Sidebar Header */}
      <div className="p-4 border-b border-opacity-20 border-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
              <span className="text-sm font-bold text-white">
                {config.APP.NAME.charAt(0)}
              </span>
            </div>
            {variant !== 'compact' && (
              <div>
                <h2 className="text-lg font-semibold text-white">{config.APP.NAME}</h2>
                {currentUser && (
                  <p className="text-xs text-white opacity-75">{currentUser.role}</p>
                )}
              </div>
            )}
          </div>

          {onToggle && (
            <button
              onClick={onToggle}
              className="md:hidden text-white hover:bg-white hover:bg-opacity-20 p-1 rounded focus:outline-none"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {navigationItems.map((item) => (
          <div key={item.key}>
            {item.submenu ? (
              <div>
                <button
                  onClick={() => toggleSubmenu(item.key)}
                  className="w-full flex items-center justify-between px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors duration-200 text-white"
                >
                  <div className="flex items-center space-x-3">
                    {item.icon}
                    {variant !== 'compact' && (
                      <span className="text-sm font-medium text-white">{item.label}</span>
                    )}
                  </div>
                  {variant !== 'compact' && (
                    <svg
                      className={`w-4 h-4 transition-transform duration-200 ${
                        expandedMenus[item.key] ? 'rotate-180' : ''
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  )}
                </button>

                {expandedMenus[item.key] && variant !== 'compact' && (
                  <div className="ml-6 mt-2 space-y-1">
                    {item.submenu.map((subItem) => (
                      <Link
                        key={subItem.key}
                        to={subItem.path}
                        className={`block px-3 py-2 text-sm rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors duration-200 text-white ${
                          isActiveRoute(subItem.path) ? 'bg-white bg-opacity-20' : ''
                        }`}
                      >
                        {subItem.label}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <Link
                to={item.path}
                className={`flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors duration-200 text-white ${
                  isActiveRoute(item.path) ? 'bg-white bg-opacity-20' : ''
                }`}
              >
                {item.icon}
                {variant !== 'compact' && (
                  <span className="text-sm font-medium text-white">{item.label}</span>
                )}
              </Link>
            )}
          </div>
        ))}
      </nav>

      {/* User Info & Logout */}
      <div className="p-4 border-t border-opacity-20 border-white">
        {currentUser && variant !== 'compact' && (
          <div className="mb-3 p-3 bg-white bg-opacity-10 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <span className="text-sm font-semibold text-white">
                  {currentUser.username?.charAt(0).toUpperCase() || 'U'}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{currentUser.username}</p>
                <p className="text-xs text-white opacity-75 truncate">{currentUser.email}</p>
              </div>
            </div>
          </div>
        )}

        <Button
          onClick={handleLogout}
          variant="secondary"
          size="sm"
          fullWidth={variant !== 'compact'}
          userRole={currentUser?.role?.toLowerCase().replace(' ', '_')}
          className="bg-white bg-opacity-20 hover:bg-white hover:bg-opacity-30 text-white border-white border-opacity-30 focus:text-white"
        >
          {variant === 'compact' ? (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          ) : (
            'Logout'
          )}
        </Button>
      </div>
    </aside>
  );
};

export default Sidebar;
