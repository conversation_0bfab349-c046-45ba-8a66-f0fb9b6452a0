// /**
//  * Super Admin Dashboard Page
//  *
//  * This page shows the dashboard for Super Admin users.
//  *
//  * English: This page shows the Super Admin dashboard with user and course management
//  * Tanglish: Indha page Super Admin-kku dashboard-a display pannum, user and course management-oda
//  */

// import { useState, useEffect } from 'react';
// import DashboardLayout from '../components/DashboardLayout';
// import userService from '../services/userService';
// import courseService from '../services/courseService';
// import studentService from '../services/studentService';
// import parentService from '../services/parentService';
// import { useAuth } from '../contexts/AuthContext';

// const SuperAdminDashboard = () => {
//   // Get the current user from auth context
//   const { currentUser } = useAuth();

//   // State for tabs
//   const [activeTab, setActiveTab] = useState('users');

//   // State for users
//   const [users, setUsers] = useState([]);
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState('');

//   // State for user form
//   const [newUser, setNewUser] = useState({
//     username: '',
//     password: '',
//     email: '',
//     role: 'Admin',
//     is_admin: false,
//     course: '' // Added course field for teachers
//   });

//   // State for courses
//   const [courses, setCourses] = useState([]);
//   const [loadingCourses, setLoadingCourses] = useState(false);
//   const [courseError, setCourseError] = useState('');

//   // State for course form
//   const [newCourse, setNewCourse] = useState({
//     name: '',
//     description: ''
//   });

//   // State for students
//   const [students, setStudents] = useState([]);
//   const [loadingStudents, setLoadingStudents] = useState(false);
//   const [studentError, setStudentError] = useState('');

//   // State for student form
//   const [newStudent, setNewStudent] = useState({
//     user_id: '',
//     first_name: '',
//     last_name: '',
//     date_of_birth: '',
//     address: '',
//     phone: ''
//   });

//   // State for parents
//   const [parents, setParents] = useState([]);
//   const [loadingParents, setLoadingParents] = useState(false);
//   const [parentError, setParentError] = useState('');

//   // State for parent form
//   const [newParent, setNewParent] = useState({
//     user_id: '',
//     first_name: '',
//     last_name: '',
//     occupation: '',
//     address: '',
//     phone: ''
//   });

//   // State for parent-student mapping
//   const [newMapping, setNewMapping] = useState({
//     parent_id: '',
//     student_id: '',
//     relationship: 'Parent'
//   });

//   // State for existing parent mappings
//   const [existingParents, setExistingParents] = useState([]);

//   // State for course mapping
//   const [courseMapping, setCourseMapping] = useState({
//     student_id: '',
//     course_id: ''
//   });

//   /**
//    * Load users from API
//    *
//    * English: This function loads all users from the API
//    * Tanglish: Indha function API-la irundhu ella users-um load pannum
//    */
//   const loadUsers = async () => {
//     try {
//       setLoading(true);
//       setError('');

//       const data = await userService.getUsers();
//       setUsers(data.users || []);
//     } catch (error) {
//       setError(error.error || 'Failed to load users');
//     } finally {
//       setLoading(false);
//     }
//   };

//   /**
//    * Load courses from API
//    *
//    * English: This function loads all courses from the API
//    * Tanglish: Indha function API-la irundhu ella courses-um load pannum
//    */
//   const loadCourses = async () => {
//     try {
//       setLoadingCourses(true);
//       setCourseError('');

//       const data = await courseService.getCourses();
//       setCourses(data.courses || []);
//     } catch (error) {
//       setCourseError(error.error || 'Failed to load courses');
//     } finally {
//       setLoadingCourses(false);
//     }
//   };

//   /**
//    * Handle user form input change
//    *
//    * @param {Event} e - Input change event
//    *
//    * English: This function handles changes to the user form inputs
//    * Tanglish: Indha function user form inputs-la changes-a handle pannum
//    */
//   const handleUserInputChange = (e) => {
//     const { name, value, type, checked } = e.target;
//     setNewUser({
//       ...newUser,
//       [name]: type === 'checkbox' ? checked : value
//     });
//   };

//   /**
//    * Handle course form input change
//    *
//    * @param {Event} e - Input change event
//    *
//    * English: This function handles changes to the course form inputs
//    * Tanglish: Indha function course form inputs-la changes-a handle pannum
//    */
//   const handleCourseInputChange = (e) => {
//     const { name, value } = e.target;
//     setNewCourse({
//       ...newCourse,
//       [name]: value
//     });
//   };

//   /**
//    * Handle user form submission
//    *
//    * @param {Event} e - Form submit event
//    *
//    * English: This function handles user form submission to register a new user
//    * Tanglish: Indha function user form submit-a handle panni puthusa oru user-a register pannum
//    */
//   const handleUserSubmit = async (e) => {
//     e.preventDefault();

//     try {
//       setLoading(true);
//       setError('');

//       await userService.registerUser({
//         ...newUser,
//         main_code: currentUser?.main_code // Use the super admin's main_code
//       });

//       // Reset form
//       setNewUser({
//         username: '',
//         password: '',
//         email: '',
//         role: 'Admin',
//         is_admin: false,
//         course: ''
//       });

//       // Show success message
//       setError('User registered successfully');

//       // Reload users
//       await loadUsers();
//     } catch (error) {
//       setError(error.error || 'Failed to register user');
//     } finally {
//       setLoading(false);
//     }
//   };

//   /**
//    * Handle course form submission
//    *
//    * @param {Event} e - Form submit event
//    *
//    * English: This function handles course form submission to create a new course
//    * Tanglish: Indha function course form submit-a handle panni puthusa oru course-a create pannum
//    */
//   const handleCourseSubmit = async (e) => {
//     e.preventDefault();

//     try {
//       setLoadingCourses(true);
//       setCourseError('');

//       await courseService.createCourse({
//         ...newCourse,
//         main_code: currentUser?.main_code // Use the super admin's main_code
//       });

//       // Reset form
//       setNewCourse({
//         name: '',
//         description: ''
//       });

//       // Reload courses
//       await loadCourses();
//     } catch (error) {
//       setCourseError(error.error || 'Failed to create course');
//     } finally {
//       setLoadingCourses(false);
//     }
//   };

//   /**
//    * Load students from API
//    *
//    * English: This function loads all students from the API
//    * Tanglish: Indha function API-la irundhu ella students-um load pannum
//    */
//   const loadStudents = async () => {
//     try {
//       setLoadingStudents(true);
//       setStudentError('');

//       const data = await studentService.getStudents();
//       setStudents(data.students || []);
//     } catch (error) {
//       setStudentError(error.error || 'Failed to load students');
//     } finally {
//       setLoadingStudents(false);
//     }
//   };

//   /**
//    * Load parents from API
//    *
//    * English: This function loads all parents from the API
//    * Tanglish: Indha function API-la irundhu ella parents-um load pannum
//    */
//   const loadParents = async () => {
//     try {
//       setLoadingParents(true);
//       setParentError('');

//       const data = await parentService.getParents();
//       setParents(data.parents || []);
//     } catch (error) {
//       setParentError(error.error || 'Failed to load parents');
//     } finally {
//       setLoadingParents(false);
//     }
//   };

//   /**
//    * Handle student form input change
//    *
//    * @param {Event} e - Input change event
//    *
//    * English: This function handles changes to the student form inputs
//    * Tanglish: Indha function student form inputs-la changes-a handle pannum
//    */
//   const handleStudentInputChange = (e) => {
//     const { name, value } = e.target;
//     setNewStudent({
//       ...newStudent,
//       [name]: value
//     });
//   };

//   /**
//    * Handle parent form input change
//    *
//    * @param {Event} e - Input change event
//    *
//    * English: This function handles changes to the parent form inputs
//    * Tanglish: Indha function parent form inputs-la changes-a handle pannum
//    */
//   const handleParentInputChange = (e) => {
//     const { name, value } = e.target;
//     setNewParent({
//       ...newParent,
//       [name]: value
//     });
//   };

//   /**
//    * Handle mapping form input change
//    *
//    * @param {Event} e - Input change event
//    *
//    * English: This function handles changes to the mapping form inputs
//    * Tanglish: Indha function mapping form inputs-la changes-a handle pannum
//    */
//   const handleMappingInputChange = async (e) => {
//     const { name, value } = e.target;
//     setNewMapping({
//       ...newMapping,
//       [name]: value
//     });

//     // If student_id is selected, fetch existing parents for this student
//     if (name === 'student_id' && value) {
//       try {
//         setLoadingParents(true);
//         const data = await studentService.getStudentParents(value);
//         setExistingParents(data.parents || []);

//         // If student already has parents, show a message
//         if (data.parents && data.parents.length > 0) {
//           const parentNames = data.parents.map(p => `${p.first_name} ${p.last_name} (${p.relationship})`).join(', ');
//           setParentError(`Note: This student already has the following parents mapped: ${parentNames}`);
//         } else {
//           setParentError('');
//         }
//       } catch (error) {
//         console.error('Error fetching student parents:', error);
//       } finally {
//         setLoadingParents(false);
//       }
//     }
//   };

//   /**
//    * Handle course mapping form input change
//    *
//    * @param {Event} e - Input change event
//    *
//    * English: This function handles changes to the course mapping form inputs
//    * Tanglish: Indha function course mapping form inputs-la changes-a handle pannum
//    */
//   const handleCourseMappingChange = (e) => {
//     const { name, value } = e.target;
//     setCourseMapping({
//       ...courseMapping,
//       [name]: value
//     });
//   };

//   /**
//    * Handle student registration
//    *
//    * @param {Event} e - Form submit event
//    *
//    * English: This function handles student registration
//    * Tanglish: Indha function student registration-a handle pannum
//    */
//   const handleStudentRegistration = async (e) => {
//     e.preventDefault();

//     try {
//       setLoadingStudents(true);
//       setStudentError('');

//       // First register the user with the super admin's main_code
//       const userData = await userService.registerUser({
//         ...newUser,
//         role: 'Student',
//         main_code: currentUser?.main_code // Use the super admin's main_code
//       });

//       // Then register the student with the user ID and main_code
//       await studentService.registerStudent({
//         ...newStudent,
//         user_id: userData.user.id,
//         main_code: currentUser?.main_code // Use the super admin's main_code
//       });

//       // Reset forms
//       setNewUser({
//         username: '',
//         password: '',
//         email: '',
//         role: 'Student'
//       });

//       setNewStudent({
//         user_id: '',
//         first_name: '',
//         last_name: '',
//         date_of_birth: '',
//         address: '',
//         phone: ''
//       });

//       // Show success message
//       setStudentError('Student registered successfully');

//       // Reload students
//       await loadStudents();
//     } catch (error) {
//       setStudentError(error.error || 'Failed to register student');
//     } finally {
//       setLoadingStudents(false);
//     }
//   };

//   /**
//    * Handle parent registration
//    *
//    * @param {Event} e - Form submit event
//    *
//    * English: This function handles parent registration
//    * Tanglish: Indha function parent registration-a handle pannum
//    */
//   const handleParentRegistration = async (e) => {
//     e.preventDefault();

//     try {
//       setLoadingParents(true);
//       setParentError('');

//       // First register the user with the super admin's main_code
//       const userData = await userService.registerUser({
//         ...newUser,
//         role: 'Parent',
//         main_code: currentUser?.main_code // Use the super admin's main_code
//       });

//       // Then register the parent with the user ID and main_code
//       await parentService.registerParent({
//         ...newParent,
//         user_id: userData.user.id,
//         main_code: currentUser?.main_code // Use the super admin's main_code
//       });

//       // Reset forms
//       setNewUser({
//         username: '',
//         password: '',
//         email: '',
//         role: 'Parent'
//       });

//       setNewParent({
//         user_id: '',
//         first_name: '',
//         last_name: '',
//         occupation: '',
//         address: '',
//         phone: ''
//       });

//       // Show success message
//       setParentError('Parent registered successfully');

//       // Reload parents
//       await loadParents();
//     } catch (error) {
//       setParentError(error.error || 'Failed to register parent');
//     } finally {
//       setLoadingParents(false);
//     }
//   };

//   /**
//    * Handle parent-student mapping
//    *
//    * @param {Event} e - Form submit event
//    *
//    * English: This function handles parent-student mapping
//    * Tanglish: Indha function parent-student mapping-a handle pannum
//    */
//   const handleParentStudentMapping = async (e) => {
//     e.preventDefault();

//     try {
//       setLoadingParents(true);
//       setParentError('');

//       // Make sure to pass the parameters in the correct order
//       await studentService.mapParentToStudent(
//         newMapping.parent_id,
//         newMapping.student_id,
//         newMapping.relationship || 'Parent'  // Provide a default relationship if none is selected
//       );

//       // Reset form and existing parents
//       setNewMapping({
//         parent_id: '',
//         student_id: '',
//         relationship: 'Parent'
//       });
//       setExistingParents([]);

//       // Show success message
//       setParentError('Parent mapped to student successfully');
//     } catch (error) {
//       setParentError(error.error || 'Failed to map parent to student');
//     } finally {
//       setLoadingParents(false);
//     }
//   };

//   /**
//    * Handle course mapping form submission
//    *
//    * @param {Event} e - Form submit event
//    *
//    * English: This function handles course mapping form submission
//    * Tanglish: Indha function course mapping form submit-a handle pannum
//    */
//   const handleCourseMapping = async (e) => {
//     e.preventDefault();

//     try {
//       setLoadingCourses(true);
//       setCourseError('');

//       await courseService.mapStudentToCourse(
//         courseMapping.student_id,
//         courseMapping.course_id
//       );

//       // Reset form
//       setCourseMapping({
//         student_id: '',
//         course_id: ''
//       });

//       // Show success message
//       setCourseError('Student mapped to course successfully');
//     } catch (error) {
//       setCourseError(error.error || 'Failed to map student to course');
//     } finally {
//       setLoadingCourses(false);
//     }
//   };

//   // Load data when component mounts or tab changes
//   useEffect(() => {
//     if (activeTab === 'users') {
//       loadUsers();
//     } else if (activeTab === 'courses') {
//       loadCourses();
//       loadStudents(); // Need students for course mapping
//     } else if (activeTab === 'students') {
//       loadStudents();
//     } else if (activeTab === 'parents') {
//       loadParents();
//       loadStudents(); // Need students for parent-student mapping
//     }
//   }, [activeTab]);

//   return (
//     <DashboardLayout title="Super Admin Dashboard">
//       <div className="mb-6">
//         <div className="flex flex-wrap border-b bg-gradient-to-r from-blue-100 to-purple-100 rounded-t-lg shadow-md">
//           <button
//             className={`py-3 px-5 font-semibold transition-all duration-300 ${activeTab === 'users' ? 'border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner' : 'text-gray-600 hover:text-indigo-500'}`}
//             onClick={() => setActiveTab('users')}
//           >
//             User Management
//           </button>
//           <button
//             className={`py-3 px-5 font-semibold transition-all duration-300 ${activeTab === 'courses' ? 'border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner' : 'text-gray-600 hover:text-indigo-500'}`}
//             onClick={() => setActiveTab('courses')}
//           >
//             Course Management
//           </button>
//           <button
//             className={`py-3 px-5 font-semibold transition-all duration-300 ${activeTab === 'students' ? 'border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner' : 'text-gray-600 hover:text-indigo-500'}`}
//             onClick={() => setActiveTab('students')}
//           >
//             Student Registration
//           </button>
//           <button
//             className={`py-3 px-5 font-semibold transition-all duration-300 ${activeTab === 'parents' ? 'border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner' : 'text-gray-600 hover:text-indigo-500'}`}
//             onClick={() => setActiveTab('parents')}
//           >
//             Parent Management
//           </button>
//         </div>
//       </div>

//       {activeTab === 'users' && (
//         <div>
//           <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-blue-500">
//             <h2 className="text-2xl font-bold mb-4 text-blue-700">Register New User</h2>

//             {error && (
//               <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
//                 {error}
//               </div>
//             )}

//             <form onSubmit={handleUserSubmit}>
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div className="form-group">
//                   <label htmlFor="username" className="form-label text-gray-700 font-medium">Username</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
//                     id="username"
//                     name="username"
//                     value={newUser.username}
//                     onChange={handleUserInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="password" className="form-label text-gray-700 font-medium">Password</label>
//                   <input
//                     type="password"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
//                     id="password"
//                     name="password"
//                     value={newUser.password}
//                     onChange={handleUserInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="email" className="form-label text-gray-700 font-medium">Email</label>
//                   <input
//                     type="email"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
//                     id="email"
//                     name="email"
//                     value={newUser.email}
//                     onChange={handleUserInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="role" className="form-label text-gray-700 font-medium">Role</label>
//                   <select
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
//                     id="role"
//                     name="role"
//                     value={newUser.role}
//                     onChange={handleUserInputChange}
//                     required
//                   >
//                     <option value="Admin">Admin</option>
//                     <option value="Teacher">Teacher</option>
//                     <option value="Student">Student</option>
//                     <option value="Parent">Parent</option>
//                   </select>
//                 </div>

//                 {/* Show course dropdown only for Teacher role */}
//                 {newUser.role === 'Teacher' && (
//                   <div className="form-group">
//                     <label htmlFor="course" className="form-label text-gray-700 font-medium">Course</label>
//                     <select
//                       className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
//                       id="course"
//                       name="course"
//                       value={newUser.course}
//                       onChange={handleUserInputChange}
//                       required={newUser.role === 'Teacher'}
//                     >
//                       <option value="">Select Course</option>
//                       <option value="Neet">Neet</option>
//                       <option value="Jee">Jee</option>
//                     </select>
//                   </div>
//                 )}

//                 <div className="form-group">
//                   <label className="inline-flex items-center mt-4">
//                     <input
//                       type="checkbox"
//                       className="form-checkbox rounded text-blue-600 focus:ring-blue-500"
//                       id="is_admin"
//                       name="is_admin"
//                       checked={newUser.is_admin}
//                       onChange={handleUserInputChange}
//                     />
//                     <span className="ml-2 text-gray-700">Is Admin (for Teachers)</span>
//                   </label>
//                 </div>
//               </div>

//               <button type="submit" className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loading}>
//                 {loading ? 'Registering...' : 'Register User'}
//               </button>
//             </form>
//           </div>

//           <div className="card bg-white rounded-lg shadow-lg p-6">
//             <h2 className="text-2xl font-bold mb-4 text-blue-700">All Users</h2>

//             {loading ? (
//               <p className="text-gray-600">Loading users...</p>
//             ) : (
//               <div className="overflow-x-auto">
//                 <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
//                   <thead className="bg-gray-50">
//                     <tr>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Is Admin</th>
//                     </tr>
//                   </thead>
//                   <tbody className="divide-y divide-gray-200">
//                     {users.map((user) => (
//                       <tr key={user.id} className="hover:bg-gray-50">
//                         <td className="py-3 px-4 text-sm text-gray-900">{user.id}</td>
//                         <td className="py-3 px-4 text-sm text-gray-900 font-medium">{user.username}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{user.email}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">
//                           <span className={`px-2 py-1 rounded-full text-xs font-medium ${
//                             user.role === 'Super Admin' ? 'bg-red-100 text-red-800' :
//                             user.role === 'Admin' ? 'bg-blue-100 text-blue-800' :
//                             user.role === 'Teacher' ? 'bg-green-100 text-green-800' :
//                             user.role === 'Student' ? 'bg-purple-100 text-purple-800' :
//                             'bg-teal-100 text-teal-800'
//                           }`}>
//                             {user.role}
//                           </span>
//                         </td>
//                         <td className="py-3 px-4 text-sm text-gray-500">
//                           {user.role === 'Teacher' && user.course ? (
//                             <span className="px-2 py-1 rounded-full bg-yellow-100 text-yellow-800 text-xs font-medium">
//                               {user.course}
//                             </span>
//                           ) : (
//                             <span className="text-gray-400">-</span>
//                           )}
//                         </td>
//                         <td className="py-3 px-4 text-sm text-gray-500">
//                           {user.is_admin ?
//                             <span className="px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium">Yes</span> :
//                             <span className="px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs font-medium">No</span>
//                           }
//                         </td>
//                       </tr>
//                     ))}
//                   </tbody>
//                 </table>
//               </div>
//             )}
//           </div>
//         </div>
//       )}

//       {activeTab === 'courses' && (
//         <div>
//           <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-indigo-500">
//             <h2 className="text-2xl font-bold mb-4 text-indigo-700">Create New Course</h2>

//             {courseError && (
//               <div className={`p-4 mb-4 rounded-md ${courseError.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
//                 {courseError}
//               </div>
//             )}

//             <form onSubmit={handleCourseSubmit}>
//               <div className="grid grid-cols-1 gap-4">
//                 <div className="form-group">
//                   <label htmlFor="name" className="form-label text-gray-700 font-medium">Course Name</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
//                     id="name"
//                     name="name"
//                     value={newCourse.name}
//                     onChange={handleCourseInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="description" className="form-label text-gray-700 font-medium">Description</label>
//                   <textarea
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
//                     id="description"
//                     name="description"
//                     value={newCourse.description}
//                     onChange={handleCourseInputChange}
//                     rows="3"
//                   ></textarea>
//                 </div>
//               </div>

//               <button type="submit" className="mt-4 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loadingCourses}>
//                 {loadingCourses ? 'Creating...' : 'Create Course'}
//               </button>
//             </form>
//           </div>

//           <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-indigo-500">
//             <h2 className="text-2xl font-bold mb-4 text-indigo-700">Map Student to Course</h2>

//             {courseError && (
//               <div className={`p-4 mb-4 rounded-md ${courseError.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
//                 {courseError}
//               </div>
//             )}

//             <form onSubmit={handleCourseMapping}>
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div className="form-group">
//                   <label htmlFor="student_id" className="form-label text-gray-700 font-medium">Student</label>
//                   <select
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
//                     id="student_id"
//                     name="student_id"
//                     value={courseMapping.student_id}
//                     onChange={handleCourseMappingChange}
//                     required
//                   >
//                     <option value="">Select Student</option>
//                     {students.map((student) => (
//                       <option key={student.id} value={student.id}>
//                         {student.first_name} {student.last_name}
//                       </option>
//                     ))}
//                   </select>
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="course_id" className="form-label text-gray-700 font-medium">Course</label>
//                   <select
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
//                     id="course_id"
//                     name="course_id"
//                     value={courseMapping.course_id}
//                     onChange={handleCourseMappingChange}
//                     required
//                   >
//                     <option value="">Select Course</option>
//                     {courses.map((course) => (
//                       <option key={course.id} value={course.id}>
//                         {course.name}
//                       </option>
//                     ))}
//                   </select>
//                 </div>
//               </div>

//               <button type="submit" className="mt-4 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loadingCourses}>
//                 {loadingCourses ? 'Mapping...' : 'Map Student to Course'}
//               </button>
//             </form>
//           </div>

//           <div className="card bg-white rounded-lg shadow-lg p-6">
//             <h2 className="text-2xl font-bold mb-4 text-indigo-700">All Courses</h2>

//             {loadingCourses ? (
//               <p className="text-gray-600">Loading courses...</p>
//             ) : (
//               <div className="overflow-x-auto">
//                 <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
//                   <thead className="bg-gray-50">
//                     <tr>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
//                     </tr>
//                   </thead>
//                   <tbody className="divide-y divide-gray-200">
//                     {courses.map((course) => (
//                       <tr key={course.id} className="hover:bg-gray-50">
//                         <td className="py-3 px-4 text-sm text-gray-900">{course.id}</td>
//                         <td className="py-3 px-4 text-sm text-gray-900 font-medium">{course.name}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{course.description}</td>
//                       </tr>
//                     ))}
//                   </tbody>
//                 </table>
//               </div>
//             )}
//           </div>
//         </div>
//       )}

//       {activeTab === 'students' && (
//         <div>
//           <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500">
//             <h2 className="text-2xl font-bold mb-4 text-purple-700">Register New Student</h2>

//             {studentError && (
//               <div className={`p-4 mb-4 rounded-md ${studentError.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
//                 {studentError}
//               </div>
//             )}

//             <form onSubmit={handleStudentRegistration}>
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div className="form-group">
//                   <label htmlFor="username" className="form-label text-gray-700 font-medium">Username</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
//                     id="username"
//                     name="username"
//                     value={newUser.username}
//                     onChange={handleUserInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="password" className="form-label text-gray-700 font-medium">Password</label>
//                   <input
//                     type="password"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
//                     id="password"
//                     name="password"
//                     value={newUser.password}
//                     onChange={handleUserInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="email" className="form-label text-gray-700 font-medium">Email</label>
//                   <input
//                     type="email"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
//                     id="email"
//                     name="email"
//                     value={newUser.email}
//                     onChange={handleUserInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="first_name" className="form-label text-gray-700 font-medium">First Name</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
//                     id="first_name"
//                     name="first_name"
//                     value={newStudent.first_name}
//                     onChange={handleStudentInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="last_name" className="form-label text-gray-700 font-medium">Last Name</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
//                     id="last_name"
//                     name="last_name"
//                     value={newStudent.last_name}
//                     onChange={handleStudentInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="date_of_birth" className="form-label text-gray-700 font-medium">Date of Birth</label>
//                   <input
//                     type="date"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
//                     id="date_of_birth"
//                     name="date_of_birth"
//                     value={newStudent.date_of_birth}
//                     onChange={handleStudentInputChange}
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="address" className="form-label text-gray-700 font-medium">Address</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
//                     id="address"
//                     name="address"
//                     value={newStudent.address}
//                     onChange={handleStudentInputChange}
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="phone" className="form-label text-gray-700 font-medium">Phone</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
//                     id="phone"
//                     name="phone"
//                     value={newStudent.phone}
//                     onChange={handleStudentInputChange}
//                   />
//                 </div>
//               </div>

//               <button type="submit" className="mt-4 px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loadingStudents}>
//                 {loadingStudents ? 'Registering...' : 'Register Student'}
//               </button>
//             </form>
//           </div>

//           <div className="card bg-white rounded-lg shadow-lg p-6">
//             <h2 className="text-2xl font-bold mb-4 text-purple-700">All Students</h2>

//             {loadingStudents ? (
//               <p className="text-gray-600">Loading students...</p>
//             ) : (
//               <div className="overflow-x-auto">
//                 <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
//                   <thead className="bg-gray-50">
//                     <tr>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date of Birth</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
//                     </tr>
//                   </thead>
//                   <tbody className="divide-y divide-gray-200">
//                     {students.map((student) => (
//                       <tr key={student.id} className="hover:bg-gray-50">
//                         <td className="py-3 px-4 text-sm text-gray-900">{student.id}</td>
//                         <td className="py-3 px-4 text-sm text-gray-900 font-medium">{student.first_name} {student.last_name}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{student.date_of_birth}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{student.address}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{student.phone}</td>
//                       </tr>
//                     ))}
//                   </tbody>
//                 </table>
//               </div>
//             )}
//           </div>
//         </div>
//       )}

//       {activeTab === 'parents' && (
//         <div>
//           <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-teal-500">
//             <h2 className="text-2xl font-bold mb-4 text-teal-700">Register New Parent</h2>

//             {parentError && (
//               <div className={`p-4 mb-4 rounded-md ${parentError.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
//                 {parentError}
//               </div>
//             )}

//             <form onSubmit={handleParentRegistration}>
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div className="form-group">
//                   <label htmlFor="username" className="form-label text-gray-700 font-medium">Username</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="username"
//                     name="username"
//                     value={newUser.username}
//                     onChange={handleUserInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="password" className="form-label text-gray-700 font-medium">Password</label>
//                   <input
//                     type="password"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="password"
//                     name="password"
//                     value={newUser.password}
//                     onChange={handleUserInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="email" className="form-label text-gray-700 font-medium">Email</label>
//                   <input
//                     type="email"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="email"
//                     name="email"
//                     value={newUser.email}
//                     onChange={handleUserInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="first_name" className="form-label text-gray-700 font-medium">First Name</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="first_name"
//                     name="first_name"
//                     value={newParent.first_name}
//                     onChange={handleParentInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="last_name" className="form-label text-gray-700 font-medium">Last Name</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="last_name"
//                     name="last_name"
//                     value={newParent.last_name}
//                     onChange={handleParentInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="occupation" className="form-label text-gray-700 font-medium">Occupation</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="occupation"
//                     name="occupation"
//                     value={newParent.occupation}
//                     onChange={handleParentInputChange}
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="address" className="form-label text-gray-700 font-medium">Address</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="address"
//                     name="address"
//                     value={newParent.address}
//                     onChange={handleParentInputChange}
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="phone" className="form-label text-gray-700 font-medium">Phone</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="phone"
//                     name="phone"
//                     value={newParent.phone}
//                     onChange={handleParentInputChange}
//                   />
//                 </div>
//               </div>

//               <button type="submit" className="mt-4 px-6 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loadingParents}>
//                 {loadingParents ? 'Registering...' : 'Register Parent'}
//               </button>
//             </form>
//           </div>

//           <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-teal-500">
//             <h2 className="text-2xl font-bold mb-4 text-teal-700">Map Parent to Student</h2>

//             {parentError && (
//               <div className={`p-4 mb-4 rounded-md ${parentError.includes('successfully') ? 'bg-green-100 text-green-700' : parentError.includes('Note:') ? 'bg-blue-100 text-blue-700' : 'bg-red-100 text-red-700'}`} role="alert">
//                 {parentError}
//               </div>
//             )}

//             <form onSubmit={handleParentStudentMapping}>
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div className="form-group">
//                   <label htmlFor="student_id" className="form-label text-gray-700 font-medium">Student</label>
//                   <select
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="student_id"
//                     name="student_id"
//                     value={newMapping.student_id}
//                     onChange={handleMappingInputChange}
//                     required
//                   >
//                     <option value="">Select Student</option>
//                     {students.map((student) => (
//                       <option key={student.id} value={student.id}>
//                         {student.first_name} {student.last_name}
//                       </option>
//                     ))}
//                   </select>
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="parent_id" className="form-label text-gray-700 font-medium">Parent</label>
//                   <select
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="parent_id"
//                     name="parent_id"
//                     value={newMapping.parent_id}
//                     onChange={handleMappingInputChange}
//                     required
//                   >
//                     <option value="">Select Parent</option>
//                     {parents.map((parent) => (
//                       <option key={parent.id} value={parent.id}>
//                         {parent.first_name} {parent.last_name}
//                       </option>
//                     ))}
//                   </select>
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="relationship" className="form-label text-gray-700 font-medium">Relationship</label>
//                   <select
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50"
//                     id="relationship"
//                     name="relationship"
//                     value={newMapping.relationship}
//                     onChange={handleMappingInputChange}
//                     required
//                   >
//                     <option value="Parent">Parent</option>
//                     <option value="Father">Father</option>
//                     <option value="Mother">Mother</option>
//                     <option value="Guardian">Guardian</option>
//                   </select>
//                 </div>
//               </div>

//               {existingParents.length > 0 && (
//                 <div className="mt-4 p-4 bg-blue-50 rounded-md border border-blue-200">
//                   <h3 className="text-lg font-semibold text-blue-700 mb-2">Existing Parents for Selected Student</h3>
//                   <ul className="list-disc pl-5 space-y-1">
//                     {existingParents.map((parent) => (
//                       <li key={parent.id} className="text-blue-600">
//                         {parent.first_name} {parent.last_name} - <span className="font-medium">{parent.relationship}</span>
//                       </li>
//                     ))}
//                   </ul>
//                 </div>
//               )}

//               <button type="submit" className="mt-4 px-6 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loadingParents}>
//                 {loadingParents ? 'Mapping...' : 'Map Parent to Student'}
//               </button>
//             </form>
//           </div>

//           <div className="card bg-white rounded-lg shadow-lg p-6">
//             <h2 className="text-2xl font-bold mb-4 text-teal-700">All Parents</h2>

//             {loadingParents ? (
//               <p className="text-gray-600">Loading parents...</p>
//             ) : (
//               <div className="overflow-x-auto">
//                 <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
//                   <thead className="bg-gray-50">
//                     <tr>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Occupation</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
//                     </tr>
//                   </thead>
//                   <tbody className="divide-y divide-gray-200">
//                     {parents.map((parent) => (
//                       <tr key={parent.id} className="hover:bg-gray-50">
//                         <td className="py-3 px-4 text-sm text-gray-900">{parent.id}</td>
//                         <td className="py-3 px-4 text-sm text-gray-900 font-medium">{parent.first_name} {parent.last_name}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{parent.occupation}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{parent.address}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{parent.phone}</td>
//                       </tr>
//                     ))}
//                   </tbody>
//                 </table>
//               </div>
//             )}
//           </div>
//         </div>
//       )}
//     </DashboardLayout>
//   );
// };

// export default SuperAdminDashboard;






/**
 * Super Admin Dashboard Page
 *
 * This page shows the dashboard for Super Admin users.
 *
 * English: This page shows the Super Admin dashboard with user and course management
 * Tanglish: Indha page Super Admin-kku dashboard-a display pannum, user and course management-oda
 */

import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../../components/field/Button';
import DatepickerComponent from '../../components/field/DatepickerComponent';
import Input from '../../components/field/Input';
import { userService } from '../../services/userService';
import { courseService } from '../../services/courseService';
import { studentService } from '../../services/studentService';
import { parentService } from '../../services/parentService';
import { useAuth } from '../../contexts/AuthContext';

const SuperAdminDashboard = () => {
  // Get the current user from auth context
  const { currentUser } = useAuth();
  const location = useLocation();

  // Get active section from URL parameters
  const searchParams = new URLSearchParams(location.search);
  const activeSection = searchParams.get('tab') || 'dashboard';

  // State for users
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // State for user form
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    email: '',
    role: 'Admin',
    is_admin: false,
    course: ''
  });

  // State for courses
  const [courses, setCourses] = useState([]);
  const [loadingCourses, setLoadingCourses] = useState(false);
  const [courseError, setCourseError] = useState('');

  // State for course form
  const [newCourse, setNewCourse] = useState({
    name: '',
    description: ''
  });

  // State for students
  const [students, setStudents] = useState([]);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [studentError, setStudentError] = useState('');

  // State for student form
  const [newStudent, setNewStudent] = useState({
    user_id: '',
    first_name: '',
    last_name: '',
    date_of_birth: '',
    address: '',
    phone: ''
  });

  // State for parents
  const [parents, setParents] = useState([]);
  const [loadingParents, setLoadingParents] = useState(false);
  const [parentError, setParentError] = useState('');

  // State for parent form
  const [newParent, setNewParent] = useState({
    user_id: '',
    first_name: '',
    last_name: '',
    occupation: '',
    address: '',
    phone: ''
  });

  // State for parent-student mapping
  const [newMapping, setNewMapping] = useState({
    parent_id: '',
    student_id: '',
    relationship: 'Parent'
  });

  // State for existing parent mappings
  const [existingParents, setExistingParents] = useState([]);

  // State for course mapping
  const [courseMapping, setCourseMapping] = useState({
    student_id: '',
    course_id: ''
  });

  /**
   * Load users from API
   */
  const loadUsers = async () => {
    try {
      setLoading(true);
      setError('');
      const data = await userService.getUsers();
      setUsers(data.users || []);
    } catch (error) {
      setError(error.error || 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load courses from API
   */
  const loadCourses = async () => {
    try {
      setLoadingCourses(true);
      setCourseError('');
      const data = await courseService.getCourses();
      setCourses(data.courses || []);
    } catch (error) {
      setCourseError(error.error || 'Failed to load courses');
    } finally {
      setLoadingCourses(false);
    }
  };

  /**
   * Handle user form input change
   */
  const handleUserInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewUser({
      ...newUser,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  /**
   * Handle course form input change
   */
  const handleCourseInputChange = (e) => {
    const { name, value } = e.target;
    setNewCourse({
      ...newCourse,
      [name]: value
    });
  };

  /**
   * Handle user form submission
   */
  const handleUserSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError('');
      await userService.registerUser({
        ...newUser,
        main_code: currentUser?.main_code
      });
      setNewUser({
        username: '',
        password: '',
        email: '',
        role: 'Admin',
        is_admin: false,
        course: ''
      });
      setError('User registered successfully');
      await loadUsers();
    } catch (error) {
      setError(error.error || 'Failed to register user');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle course form submission
   */
  const handleCourseSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoadingCourses(true);
      setCourseError('');
      await courseService.createCourse({
        ...newCourse,
        main_code: currentUser?.main_code
      });
      setNewCourse({
        name: '',
        description: ''
      });
      await loadCourses();
    } catch (error) {
      setCourseError(error.error || 'Failed to create course');
    } finally {
      setLoadingCourses(false);
    }
  };

  /**
   * Load students from API
   */
  const loadStudents = async () => {
    try {
      setLoadingStudents(true);
      setStudentError('');
      const data = await studentService.getStudents();
      setStudents(data.students || []);
    } catch (error) {
      setStudentError(error.error || 'Failed to load students');
    } finally {
      setLoadingStudents(false);
    }
  };

  /**
   * Load parents from API
   */
  const loadParents = async () => {
    try {
      setLoadingParents(true);
      setParentError('');
      const data = await parentService.getParents();
      setParents(data.parents || []);
    } catch (error) {
      setParentError(error.error || 'Failed to load parents');
    } finally {
      setLoadingParents(false);
    }
  };

  /**
   * Handle student form input change
   */
  const handleStudentInputChange = (e) => {
    const { name, value } = e.target;
    setNewStudent({
      ...newStudent,
      [name]: value
    });
  };

  /**
   * Handle parent form input change
   */
  const handleParentInputChange = (e) => {
    const { name, value } = e.target;
    setNewParent({
      ...newParent,
      [name]: value
    });
  };

  /**
   * Handle mapping form input change
   */
  const handleMappingInputChange = async (e) => {
    const { name, value } = e.target;
    setNewMapping({
      ...newMapping,
      [name]: value
    });
    if (name === 'student_id' && value) {
      try {
        setLoadingParents(true);
        const data = await studentService.getStudentParents(value);
        setExistingParents(data.parents || []);
        if (data.parents && data.parents.length > 0) {
          const parentNames = data.parents.map(p => `${p.first_name} ${p.last_name} (${p.relationship})`).join(', ');
          setParentError(`Note: This student already has the following parents mapped: ${parentNames}`);
        } else {
          setParentError('');
        }
      } catch (error) {
        console.error('Error fetching student parents:', error);
      } finally {
        setLoadingParents(false);
      }
    }
  };

  /**
   * Handle course mapping form input change
   */
  const handleCourseMappingChange = (e) => {
    const { name, value } = e.target;
    setCourseMapping({
      ...courseMapping,
      [name]: value
    });
  };

  /**
   * Handle student registration
   */
  const handleStudentRegistration = async (e) => {
    e.preventDefault();
    try {
      setLoadingStudents(true);
      setStudentError('');
      const userData = await userService.registerUser({
        ...newUser,
        role: 'Student',
        main_code: currentUser?.main_code
      });
      await studentService.registerStudent({
        ...newStudent,
        user_id: userData.user.id,
        main_code: currentUser?.main_code
      });
      setNewUser({
        username: '',
        password: '',
        email: '',
        role: 'Student'
      });
      setNewStudent({
        user_id: '',
        first_name: '',
        last_name: '',
        date_of_birth: '',
        address: '',
        phone: ''
      });
      setStudentError('Student registered successfully');
      await loadStudents();
    } catch (error) {
      setStudentError(error.error || 'Failed to register student');
    } finally {
      setLoadingStudents(false);
    }
  };

  /**
   * Handle parent registration
   */
  const handleParentRegistration = async (e) => {
    e.preventDefault();
    try {
      setLoadingParents(true);
      setParentError('');
      const userData = await userService.registerUser({
        ...newUser,
        role: 'Parent',
        main_code: currentUser?.main_code
      });
      await parentService.registerParent({
        ...newParent,
        user_id: userData.user.id,
        main_code: currentUser?.main_code
      });
      setNewUser({
        username: '',
        password: '',
        email: '',
        role: 'Parent'
      });
      setNewParent({
        user_id: '',
        first_name: '',
        last_name: '',
        occupation: '',
        address: '',
        phone: ''
      });
      setParentError('Parent registered successfully');
      await loadParents();
    } catch (error) {
      setParentError(error.error || 'Failed to register parent');
    } finally {
      setLoadingParents(false);
    }
  };

  /**
   * Handle parent-student mapping
   */
  const handleParentStudentMapping = async (e) => {
    e.preventDefault();
    try {
      setLoadingParents(true);
      setParentError('');
      await studentService.mapParentToStudent(
        newMapping.parent_id,
        newMapping.student_id,
        newMapping.relationship || 'Parent'
      );
      setNewMapping({
        parent_id: '',
        student_id: '',
        relationship: 'Parent'
      });
      setExistingParents([]);
      setParentError('Parent mapped to student successfully');
    } catch (error) {
      setParentError(error.error || 'Failed to map parent to student');
    } finally {
      setLoadingParents(false);
    }
  };

  /**
   * Handle course mapping form submission
   */
  const handleCourseMapping = async (e) => {
    e.preventDefault();
    try {
      setLoadingCourses(true);
      setCourseError('');
      await courseService.mapStudentToCourse(
        courseMapping.student_id,
        courseMapping.course_id
      );
      setCourseMapping({
        student_id: '',
        course_id: ''
      });
      setCourseError('Student mapped to course successfully');
    } catch (error) {
      setCourseError(error.error || 'Failed to map student to course');
    } finally {
      setLoadingCourses(false);
    }
  };

  // Load data when component mounts or section changes
  useEffect(() => {
    if (activeSection === 'dashboard') {
      // Load all data for dashboard overview
      loadUsers();
      loadCourses();
      loadStudents();
      loadParents();
    } else if (activeSection === 'users') {
      loadUsers();
    } else if (activeSection === 'courses') {
      loadCourses();
      loadStudents();
    } else if (activeSection === 'students') {
      loadStudents();
    } else if (activeSection === 'parents') {
      loadParents();
      loadStudents();
    }
  }, [activeSection]);

  // Render dashboard overview
  const renderDashboard = () => (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Students</h3>
          <p className="text-3xl font-bold text-purple-600">{students.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Courses</h3>
          <p className="text-3xl font-bold text-green-600">{courses.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Parents</h3>
          <p className="text-3xl font-bold text-teal-600">{parents.length}</p>
        </div>
      </div>
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Welcome to Super Admin Dashboard</h3>
        <p className="text-gray-600">Use the sidebar navigation to manage users, courses, students, and parents.</p>
      </div>
    </div>
  );

  return (
    <DashboardLayout title="Super Admin Dashboard">
      <div className="container mx-auto px-4 py-8">
        {activeSection === 'dashboard' && renderDashboard()}

        {activeSection === 'users' && (
          <div className="space-y-8">
            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">Register New User</h2>

              {error && (
                <div
                  className={`mb-6 rounded-lg p-4 ${
                    error.includes('successfully')
                      ? 'bg-green-50 text-green-700'
                      : 'bg-red-50 text-red-700'
                  }`}
                  role="alert"
                >
                  {error}
                </div>
              )}

              <form onSubmit={handleUserSubmit} className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {[
                    { id: 'username', label: 'Username', type: 'text' },
                    { id: 'password', label: 'Password', type: 'password' },
                    { id: 'email', label: 'Email', type: 'email' },
                  ].map((field) => (
                    <Input
                      key={field.id}
                      id={field.id}
                      name={field.id}
                      type={field.type}
                      label={field.label}
                      value={newUser[field.id]}
                      onChange={handleUserInputChange}
                      required
                      userRole="super_admin"
                    />
                  ))}

                  <div className="space-y-2">
                    <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                      Role
                    </label>
                    <select
                      className="w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0"
                      id="role"
                      name="role"
                      value={newUser.role}
                      onChange={handleUserInputChange}
                      required
                    >
                      {['Admin', 'Teacher', 'Student', 'Parent'].map((role) => (
                        <option key={role} value={role}>
                          {role}
                        </option>
                      ))}
                    </select>
                  </div>

                  {newUser.role === 'Teacher' && (
                    <div className="space-y-2">
                      <label htmlFor="course" className="block text-sm font-medium text-gray-700">
                        Course
                      </label>
                      <select
                        className="w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0"
                        id="course"
                        name="course"
                        value={newUser.course}
                        onChange={handleUserInputChange}
                        required
                      >
                        <option value="">Select Course</option>
                        {['Neet', 'Jee'].map((course) => (
                          <option key={course} value={course}>
                            {course}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      id="is_admin"
                      name="is_admin"
                      checked={newUser.is_admin}
                      onChange={handleUserInputChange}
                    />
                    <label htmlFor="is_admin" className="text-sm text-gray-700">
                      Is Admin (for Teachers)
                    </label>
                  </div>
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  size="md"
                  userRole="super_admin"
                  disabled={loading}
                  loading={loading}
                >
                  Register User
                </Button>
              </form>
            </div>

            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">All Users</h2>

              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <svg
                    className="h-8 w-8 animate-spin text-blue-600"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"
                    />
                  </svg>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b bg-gray-50">
                        {['ID', 'Username', 'Email', 'Role', 'Course', 'Is Admin'].map((header) => (
                          <th
                            key={header}
                            className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {users.map((user) => (
                        <tr
                          key={user.id}
                          className="border-b transition-colors hover:bg-gray-50"
                        >
                          <td className="px-6 py-4 text-sm text-gray-900">{user.id}</td>
                          <td className="px-6 py-4 text-sm font-medium text-gray-900">
                            {user.username}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">{user.email}</td>
                          <td className="px-6 py-4">
                            <span
                              className={`inline-flex rounded-full px-3 py-1 text-xs font-medium ${
                                user.role === 'Super Admin'
                                  ? 'bg-red-100 text-red-800'
                                  : user.role === 'Admin'
                                  ? 'bg-blue-100 text-blue-800'
                                  : user.role === 'Teacher'
                                  ? 'bg-green-100 text-green-800'
                                  : user.role === 'Student'
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-teal-100 text-teal-800'
                              }`}
                            >
                              {user.role}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm">
                            {user.role === 'Teacher' && user.course ? (
                              <span className="rounded-full bg-yellow-100 px-3 py-1 text-xs font-medium text-yellow-800">
                                {user.course}
                              </span>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                          <td className="px-6 py-4">
                            <span
                              className={`inline-flex rounded-full px-3 py-1 text-xs font-medium ${
                                user.is_admin
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}
                            >
                              {user.is_admin ? 'Yes' : 'No'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {activeSection === 'courses' && (
          <div className="space-y-8">
            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">Create New Course</h2>

              {courseError && (
                <div
                  className={`mb-6 rounded-lg p-4 ${
                    courseError.includes('successfully')
                      ? 'bg-green-50 text-green-700'
                      : 'bg-red-50 text-red-700'
                  }`}
                  role="alert"
                >
                  {courseError}
                </div>
              )}

              <form onSubmit={handleCourseSubmit} className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    label="Course Name"
                    value={newCourse.name}
                    onChange={handleCourseInputChange}
                    required
                    userRole="super_admin"
                  />

                  <div className="space-y-2">
                    <label
                      htmlFor="description"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Description
                    </label>
                    <textarea
                      className="w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0"
                      id="description"
                      name="description"
                      value={newCourse.description}
                      onChange={handleCourseInputChange}
                      rows="4"
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  size="md"
                  userRole="super_admin"
                  disabled={loadingCourses}
                  loading={loadingCourses}
                >
                  Create Course
                </Button>
              </form>
            </div>

            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">Map Student to Course</h2>

              {courseError && (
                <div
                  className={`mb-6 rounded-lg p-4 ${
                    courseError.includes('successfully')
                      ? 'bg-green-50 text-green-700'
                      : 'bg-red-50 text-red-700'
                  }`}
                  role="alert"
                >
                  {courseError}
                </div>
              )}

              <form onSubmit={handleCourseMapping} className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div className="space-y-2">
                    <label
                      htmlFor="student_id"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Student
                    </label>
                    <select
                      className="w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0"
                      id="student_id"
                      name="student_id"
                      value={courseMapping.student_id}
                      onChange={handleCourseMappingChange}
                      required
                    >
                      <option value="">Select Student</option>
                      {students.map((student) => (
                        <option key={student.id} value={student.id}>
                          {student.first_name} {student.last_name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label
                      htmlFor="course_id"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Course
                    </label>
                    <select
                      className="w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0"
                      id="course_id"
                      name="course_id"
                      value={courseMapping.course_id}
                      onChange={handleCourseMappingChange}
                      required
                    >
                      <option value="">Select Course</option>
                      {courses.map((course) => (
                        <option key={course.id} value={course.id}>
                          {course.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  size="md"
                  userRole="super_admin"
                  disabled={loadingCourses}
                  loading={loadingCourses}
                >
                  Map Student to Course
                </Button>
              </form>
            </div>

            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">All Courses</h2>

              {loadingCourses ? (
                <div className="flex items-center justify-center py-8">
                  <svg
                    className="h-8 w-8 animate-spin text-blue-600"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"
                    />
                  </svg>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b bg-gray-50">
                        {['ID', 'Name', 'Description'].map((header) => (
                          <th
                            key={header}
                            className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {courses.map((course) => (
                        <tr
                          key={course.id}
                          className="border-b transition-colors hover:bg-gray-50"
                        >
                          <td className="px-6 py-4 text-sm text-gray-900">{course.id}</td>
                          <td className="px-6 py-4 text-sm font-medium text-gray-900">
                            {course.name}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">
                            {course.description}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {activeSection === 'students' && (
          <div className="space-y-8">
            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">Register New Student</h2>

              {studentError && (
                <div
                  className={`mb-6 rounded-lg p-4 ${
                    studentError.includes('successfully')
                      ? 'bg-green-50 text-green-700'
                      : 'bg-red-50 text-red-700'
                  }`}
                  role="alert"
                >
                  {studentError}
                </div>
              )}

              <form onSubmit={handleStudentRegistration} className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {[
                    { id: 'username', label: 'Username', type: 'text', state: newUser },
                    { id: 'password', label: 'Password', type: 'password', state: newUser },
                    { id: 'email', label: 'Email', type: 'email', state: newUser },
                    { id: 'first_name', label: 'First Name', type: 'text', state: newStudent },
                    { id: 'last_name', label: 'Last Name', type: 'text', state: newStudent },
                    { id: 'address', label: 'Address', type: 'text', state: newStudent },
                    { id: 'phone', label: 'Phone', type: 'text', state: newStudent },
                  ].map((field) => (
                    <Input
                      key={field.id}
                      id={field.id}
                      name={field.id}
                      type={field.type}
                      label={field.label}
                      value={field.state[field.id]}
                      onChange={
                        field.state === newUser
                          ? handleUserInputChange
                          : handleStudentInputChange
                      }
                      required={field.id !== 'address' && field.id !== 'phone'}
                      userRole="super_admin"
                    />
                  ))}
                  <DatepickerComponent
                    id="date_of_birth"
                    name="date_of_birth"
                    label="Date of Birth"
                    value={newStudent.date_of_birth}
                    onChange={handleStudentInputChange}
                    userRole="super_admin"
                  />
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  size="md"
                  userRole="super_admin"
                  disabled={loadingStudents}
                  loading={loadingStudents}
                >
                  Register Student
                </Button>
              </form>
            </div>

            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">All Students</h2>

              {loadingStudents ? (
                <div className="flex items-center justify-center py-8">
                  <svg
                    className="h-8 w-8 animate-spin text-blue-600"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"
                    />
                  </svg>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b bg-gray-50">
                        {['ID', 'Name', 'Date of Birth', 'Address', 'Phone'].map((header) => (
                          <th
                            key={header}
                            className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {students.map((student) => (
                        <tr
                          key={student.id}
                          className="border-b transition-colors hover:bg-gray-50"
                        >
                          <td className="px-6 py-4 text-sm text-gray-900">{student.id}</td>
                          <td className="px-6 py-4 text-sm font-medium text-gray-900">
                            {student.first_name} {student.last_name}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">
                            {student.date_of_birth}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">{student.address}</td>
                          <td className="px-6 py-4 text-sm text-gray-600">{student.phone}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}

        {activeSection === 'parents' && (
          <div className="space-y-8">
            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">Register New Parent</h2>

              {parentError && (
                <div
                  className={`mb-6 rounded-lg p-4 ${
                    parentError.includes('successfully')
                      ? 'bg-green-50 text-green-700'
                      : parentError.includes('Note:')
                      ? 'bg-blue-50 text-blue-700'
                      : 'bg-red-50 text-red-700'
                  }`}
                  role="alert"
                >
                  {parentError}
                </div>
              )}

              <form onSubmit={handleParentRegistration} className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {[
                    { id: 'username', label: 'Username', type: 'text', state: newUser },
                    { id: 'password', label: 'Password', type: 'password', state: newUser },
                    { id: 'email', label: 'Email', type: 'email', state: newUser },
                    { id: 'first_name', label: 'First Name', type: 'text', state: newParent },
                    { id: 'last_name', label: 'Last Name', type: 'text', state: newParent },
                    { id: 'occupation', label: 'Occupation', type: 'text', state: newParent },
                    { id: 'address', label: 'Address', type: 'text', state: newParent },
                    { id: 'phone', label: 'Phone', type: 'text', state: newParent },
                  ].map((field) => (
                    <Input
                      key={field.id}
                      id={field.id}
                      name={field.id}
                      type={field.type}
                      label={field.label}
                      value={field.state[field.id]}
                      onChange={
                        field.state === newUser
                          ? handleUserInputChange
                          : handleParentInputChange
                      }
                      required={field.id !== 'occupation' && field.id !== 'address' && field.id !== 'phone'}
                      userRole="super_admin"
                    />
                  ))}
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  size="md"
                  userRole="super_admin"
                  disabled={loadingParents}
                  loading={loadingParents}
                >
                  Register Parent
                </Button>
              </form>
            </div>

            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">Map Parent to Student</h2>

              {parentError && (
                <div
                  className={`mb-6 rounded-lg p-4 ${
                    parentError.includes('successfully')
                      ? 'bg-green-50 text-green-700'
                      : parentError.includes('Note:')
                      ? 'bg-blue-50 text-blue-700'
                      : 'bg-red-50 text-red-700'
                  }`}
                  role="alert"
                >
                  {parentError}
                </div>
              )}

              <form onSubmit={handleParentStudentMapping} className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div className="space-y-2">
                    <label
                      htmlFor="student_id"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Student
                    </label>
                    <select
                      className="w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0"
                      id="student_id"
                      name="student_id"
                      value={newMapping.student_id}
                      onChange={handleMappingInputChange}
                      required
                    >
                      <option value="">Select Student</option>
                      {students.map((student) => (
                        <option key={student.id} value={student.id}>
                          {student.first_name} {student.last_name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label
                      htmlFor="parent_id"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Parent
                    </label>
                    <select
                      className="w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0"
                      id="parent_id"
                      name="parent_id"
                      value={newMapping.parent_id}
                      onChange={handleMappingInputChange}
                      required
                    >
                      <option value="">Select Parent</option>
                      {parents.map((parent) => (
                        <option key={parent.id} value={parent.id}>
                          {parent.first_name} {parent.last_name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label
                      htmlFor="relationship"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Relationship
                    </label>
                    <select
                      className="w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0"
                      id="relationship"
                      name="relationship"
                      value={newMapping.relationship}
                      onChange={handleMappingInputChange}
                      required
                    >
                      {['Parent', 'Father', 'Mother', 'Guardian'].map((rel) => (
                        <option key={rel} value={rel}>
                          {rel}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {existingParents.length > 0 && (
                  <div className="mt-6 rounded-lg bg-blue-50 p-6">
                    <h3 className="mb-3 text-lg font-semibold text-blue-800">
                      Existing Parents for Selected Student
                    </h3>
                    <ul className="list-disc space-y-2 pl-5">
                      {existingParents.map((parent) => (
                        <li key={parent.id} className="text-sm text-blue-600">
                          {parent.first_name} {parent.last_name} -{' '}
                          <span className="font-medium">{parent.relationship}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <Button
                  type="submit"
                  variant="primary"
                  size="md"
                  userRole="super_admin"
                  disabled={loadingParents}
                  loading={loadingParents}
                >
                  Map Parent to Student
                </Button>
              </form>
            </div>

            <div className="rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl">
              <h2 className="mb-6 text-2xl font-bold text-gray-800">All Parents</h2>

              {loadingParents ? (
                <div className="flex items-center justify-center py-8">
                  <svg
                    className="h-8 w-8 animate-spin text-blue-600"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"
                    />
                  </svg>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b bg-gray-50">
                        {['ID', 'Name', 'Occupation', 'Address', 'Phone'].map((header) => (
                          <th
                            key={header}
                            className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {parents.map((parent) => (
                        <tr
                          key={parent.id}
                          className="border-b transition-colors hover:bg-gray-50"
                        >
                          <td className="px-6 py-4 text-sm text-gray-900">{parent.id}</td>
                          <td className="px-6 py-4 text-sm font-medium text-gray-900">
                            {parent.first_name} {parent.last_name}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">
                            {parent.occupation}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">{parent.address}</td>
                          <td className="px-6 py-4 text-sm text-gray-600">{parent.phone}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default SuperAdminDashboard;